import cv2
import numpy as np
import argparse
import os


def align_image_with_marker(image):
    """
    Detects an ArUco marker in the image and uses it to rotate and align the image.

    Args:
        image (np.array): The original color image.

    Returns:
        tuple: (aligned_image, marker_id)
               - aligned_image (np.array): The aligned image.
               - marker_id (int or None): The ID of the found marker, or None.
    """
    print("Attempting to align image using ArUco marker...")
    # This requires the opencv-contrib-python package
    try:
        aruco_dict = cv2.aruco.getPredefinedDictionary(cv2.aruco.DICT_6X6_250)
        aruco_params = cv2.aruco.DetectorParameters()
        detector = cv2.aruco.ArucoDetector(aruco_dict, aruco_params)
    except AttributeError:
        print(
            "Error: ArUco functionality not found. Please ensure you have 'opencv-contrib-python' installed (`pip install opencv-contrib-python`)."
        )
        return image, None  # Return original image if ArUco is not available

    corners, ids, _ = detector.detectMarkers(image)

    if ids is None or len(ids) == 0:
        print("Warning: No ArUco marker found. Proceeding without alignment.")
        return image, None

    marker_id = int(ids[0][0])
    print(f"Found marker with ID: {marker_id}")

    # Get the coordinates of the first marker's corners
    marker_corners = corners[0].reshape((4, 2))
    tl, tr, _, _ = marker_corners

    # Calculate the rotation angle based on the top-left and top-right corners
    dx = tr[0] - tl[0]
    dy = tr[1] - tl[1]
    angle = np.degrees(np.arctan2(dy, dx))

    # Rotate the image to align it
    (h, w) = image.shape[:2]
    center = (w // 2, h // 2)
    rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)

    aligned_image = cv2.warpAffine(
        image,
        rotation_matrix,
        (w, h),
        flags=cv2.INTER_CUBIC,
        borderMode=cv2.BORDER_REPLICATE,
    )

    print(f"Rotated image by {angle:.2f} degrees for alignment.")
    return aligned_image, marker_id


def preprocess_for_contours(image):
    """
    Prepares the image for contour detection.
    This involves converting to grayscale, blurring, and applying an adaptive threshold.

    Args:
        image (np.array): The input color image (already aligned).

    Returns:
        np.array: A binary (black and white) image.
    """
    print("Preprocessing image for contour detection...")
    gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    # cv2.imwrite("debug_gray_image.png", gray_image)

    # --- EXPLANATION OF GAUSSIANBLUR PARAMETERS ---
    # cv2.GaussianBlur(source, ksize, sigmaX)
    #
    # ksize = (5, 5):
    # This is the size of the kernel (a 5x5 pixel block). The function
    # calculates the "blur" for each pixel based on its neighbors within this
    # block. A 5x5 kernel provides a moderate amount of blurring, which is
    # effective at smoothing out minor noise and imperfections from the scan
    # without distorting the main outlines.
    #
    # sigmaX = 0:
    # This is the standard deviation in the X direction. When set to 0,
    # OpenCV automatically calculates the ideal standard deviation based on
    # the kernel size (5x5). It's a convenient way to get a good result
    # without manual tuning.
    blurred_image = cv2.GaussianBlur(gray_image, (5, 5), 0)
    cv2.imwrite("debug_blurred_image.png", blurred_image)

    # --- EXPLANATION OF ADAPTIVETHRESHOLD PARAMETERS ---
    # cv2.adaptiveThreshold(source, maxValue, adaptiveMethod, thresholdType, blockSize, C)
    #
    # blockSize = 25:
    # This is the size of the neighborhood area (a 25x25 pixel block) that
    # is used to calculate a threshold for a given pixel. A larger blockSize
    # (like 25) helps the algorithm see the "bigger picture", making it
    # better at distinguishing the main black outline from large patches of
    # dark color.
    #
    # C = 4:
    # This is a constant that is subtracted from the calculated mean. It's a
    # fine-tuning parameter. By using 4, we make the threshold "stricter",
    # telling it to only consider pixels that are significantly darker than
    # their local neighborhood as part of the outline. This effectively
    # filters out most colored areas, even dark ones.
    binary_image = cv2.adaptiveThreshold(
        blurred_image, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY_INV, 25, 4
    )
    print("Preprocessing complete.")
    # cv2.imwrite("debug_binary_image.png", binary_image)
    return binary_image


def find_main_contour(binary_image):
    """
    Finds the main character's contour, ignoring larger surrounding frames.
    UPDATED LOGIC: Assumes the character is the THIRD-largest contour, to
    account for a page frame and a decorative inner frame.

    Args:
        binary_image (np.array): The binary image from preprocessing.

    Returns:
        np.array or None: The contour of the character, or None if not found.
    """
    print("Finding the main contour, assuming it's the 3rd largest...")
    # Use RETR_TREE to find all contours, including nested ones.
    contours, _ = cv2.findContours(binary_image, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

    if not contours:
        print("Error: No contours found.")
        return None

    # If there are fewer than 3 contours, the logic will fail.
    # This check prevents an IndexError.
    if len(contours) < 3:
        print(
            f"Warning: Found only {len(contours)} contours. The logic expects at least 3. Check the debug binary image."
        )
        # Fallback to returning the largest contour available.
        return max(contours, key=cv2.contourArea)

    # Sort contours by area in descending order.
    sorted_contours = sorted(contours, key=cv2.contourArea, reverse=True)
    print(
        f"Found {len(sorted_contours)} contours, selecting the third largest as the character."
    )

    # --- EXPLANATION OF CONTOUR SELECTION ---
    # Based on the template image, we expect a hierarchy of contours:
    #
    # sorted_contours[0]: The largest contour, assumed to be the page's outer edge.
    # sorted_contours[1]: The second-largest, assumed to be the decorative inner frame.
    # sorted_contours[2]: The third-largest, which should be the character itself (the cow).
    #
    # This logic is specific to templates with this double-frame structure.
    # outter_frame_contour = sorted_contours[0]
    # inner_frame_contour = sorted_contours[1]
    character_contour = sorted_contours[2]

    print(f"Selected character contour (Area: {cv2.contourArea(character_contour)})")

    return character_contour


def extract_object_with_alpha(original_image, contour):
    """
    Extracts the object defined by the contour and creates a transparent background.

    Args:
        original_image (np.array): The original color image (aligned).
        contour (np.array): The precise contour of the object to extract.

    Returns:
        np.array: The cropped object image with 4 channels (BGRA).
    """
    print("Extracting object and adding alpha channel...")
    if contour is None:
        print("Error: No contour provided for extraction.")
        return None

    # Create a black mask with the same dimensions as the original image.
    mask = np.zeros(original_image.shape[:2], dtype="uint8")
    # Draw the character's contour on the mask, filling it with white.
    cv2.drawContours(mask, [contour], -1, (255), thickness=cv2.FILLED)

    # Use the mask to "cut out" the character from the original image.
    extracted_bgr = cv2.bitwise_and(original_image, original_image, mask=mask)

    # Get the bounding box of the contour to crop the image to the character's size.
    x, y, w, h = cv2.boundingRect(contour)
    cropped_bgr = extracted_bgr[y : y + h, x : x + w]
    cropped_mask = mask[y : y + h, x : x + w]

    # Convert the cropped BGR image to BGRA (adding an alpha channel).
    extracted_bgra = cv2.cvtColor(cropped_bgr, cv2.COLOR_BGR2BGRA)
    # Set the alpha channel using the cropped mask.
    extracted_bgra[:, :, 3] = cropped_mask

    print("Extraction complete.")
    return extracted_bgra


# This block allows the script to be run directly from the command line.
if __name__ == "__main__":
    # Set up the command-line argument parser
    parser = argparse.ArgumentParser(
        description="A standalone script to process a scanned coloring page, isolate the character, and remove color bleed."
    )
    parser.add_argument(
        "-i",
        "--input",
        type=str,
        required=True,
        help="Path to the input scanned image file.",
    )
    parser.add_argument(
        "-o",
        "--output_dir",
        type=str,
        default="output",
        help="Directory to save the final extracted image. It will be created if it doesn't exist.",
    )
    args = parser.parse_args()

    # --- Step 0: Check inputs and create output directory ---
    if not os.path.exists(args.input):
        print(f"Error: Input file not found at '{args.input}'")
        exit()

    if not os.path.exists(args.output_dir):
        print(f"Output directory not found. Creating it at '{args.output_dir}'...")
        os.makedirs(args.output_dir)

    # --- Step 1: Read the image ---
    print(f"Processing image: {args.input}")
    original_image = cv2.imread(args.input)
    if original_image is None:
        print("Error: Could not read the image file.")
        exit()

    # --- Step 2: Align image (optional, if marker exists) ---
    aligned_image, marker_id = align_image_with_marker(original_image)

    # --- Step 3: Create a clean binary mask of the outlines ---
    clean_outline_mask = preprocess_for_contours(aligned_image)

    # --- Step 4: Find the main character's contour from the clean mask ---
    character_contour = find_main_contour(clean_outline_mask)
    if character_contour is None:
        print("Could not find a valid character contour. Exiting.")
        exit()

    # --- Step 5: Extract the colored character using the precise contour ---
    final_image = extract_object_with_alpha(aligned_image, character_contour)
    if final_image is None:
        print("Character extraction failed. Exiting.")
        exit()

    # --- Step 6: Save the final result ---
    base_filename = os.path.splitext(os.path.basename(args.input))[0]
    output_filename = f"{base_filename}_extracted.png"
    output_path = os.path.join(args.output_dir, output_filename)

    try:
        cv2.imwrite(output_path, final_image)
        print("-" * 50)
        print("🎉 SUCCESS! 🎉")
        print(f"Extracted character saved to: {output_path}")
        print("-" * 50)
    except Exception as e:
        print(f"Error saving the final image: {e}")
