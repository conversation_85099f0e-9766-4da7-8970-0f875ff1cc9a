import cv2
import numpy as np

def create_aruco_marker_template(marker_id=23, marker_size=200, filename="aruco_marker_template.png"):
    """
    Tạo ra một file ảnh chứa ArUco marker mẫu.

    Args:
        marker_id (int): ID của marker (0-249 cho bộ DICT_6X6_250).
        marker_size (int): <PERSON><PERSON><PERSON> thước của ảnh marker (pixel).
        filename (str): Tên file để lưu ảnh marker.
    """
    try:
        aruco_dict = cv2.aruco.getPredefinedDictionary(cv2.aruco.DICT_6X6_250)
        marker_image = np.zeros((marker_size, marker_size), dtype=np.uint8)
        cv2.aruco.generateImageMarker(aruco_dict, marker_id, marker_size, marker_image, 1)
        cv2.imwrite(filename, marker_image)
        print(f"Đã tạo file marker '{filename}' với ID={marker_id}.")
    except Exception as e:
        print(f"Lỗi khi tạo marker: {e}")

def align_image_with_marker(image):
    """
    Phát hiện ArUco marker trong ảnh và sử dụng nó để xoay, căn chỉnh lại ảnh.

    Args:
        image (np.array): Ảnh màu gốc (đọc bằng OpenCV).

    Returns:
        tuple: (aligned_image, marker_id)
               - aligned_image (np.array): Ảnh đã được căn chỉnh.
               - marker_id (int or None): ID của marker tìm thấy, hoặc None nếu không tìm thấy.
    """
    print("Bắt đầu căn chỉnh ảnh bằng ArUco marker...")
    aruco_dict = cv2.aruco.getPredefinedDictionary(cv2.aruco.DICT_6X6_250)
    aruco_params = cv2.aruco.DetectorParameters()
    detector = cv2.aruco.ArucoDetector(aruco_dict, aruco_params)
    
    corners, ids, _ = detector.detectMarkers(image)

    if ids is None or len(ids) == 0:
        print("Cảnh báo: Không tìm thấy ArUco marker. Trả về ảnh gốc.")
        return image, None
    
    marker_id = int(ids[0][0])
    print(f"Phát hiện marker với ID: {marker_id}")

    # Lấy tọa độ 4 góc của marker đầu tiên
    marker_corners = corners[0].reshape((4, 2))
    tl, tr, _, _ = marker_corners
    
    # Tính toán góc xoay dựa trên vector từ góc trên-trái đến trên-phải
    dx = tr[0] - tl[0]
    dy = tr[1] - tl[1]
    angle = np.degrees(np.arctan2(dy, dx))
    
    # Xoay ảnh để căn chỉnh
    (h, w) = image.shape[:2]
    center = (w // 2, h // 2)
    rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
    
    aligned_image = cv2.warpAffine(
        image, 
        rotation_matrix, 
        (w, h), 
        flags=cv2.INTER_CUBIC, 
        borderMode=cv2.BORDER_REPLICATE
    )
    
    print(f"Đã xoay ảnh một góc {angle:.2f} độ.")
    return aligned_image, marker_id

