services:
  app:
    build:
      context: ..
      dockerfile: .devcontainer/Dockerfile
    volumes:
      - ../:/workspace:cached
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**********************************/appdb
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  db:
    # Use the latest stable PostgreSQL version
    image: postgres:17
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=appdb
    ports:
      - "5432:5432"

  redis:
    # Use the latest stable Redis version
    image: redis:8.0-alpine

volumes:
  postgres_data: